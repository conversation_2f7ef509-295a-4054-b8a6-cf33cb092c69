import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { EnhancedTodoItem } from '@/types/todo';
import {
  Template,
  BookOpen,
  Plus,
  Copy,
  Edit,
  Trash2,
  Download,
  Upload,
  Star,
  Clock,
  Target,
  MoreHorizontal,
  Sparkles,
  GraduationCap,
  Calculator,
  Atom,
  Microscope,
  Globe,
  Languages,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  category: 'JEE' | 'NEET' | 'CBSE' | 'Custom';
  tasks: Partial<EnhancedTodoItem>[];
  tags: string[];
  estimatedDuration: number; // in minutes
  difficulty: 'easy' | 'medium' | 'hard';
  isPublic: boolean;
  createdAt: number;
  usageCount: number;
}

const predefinedTemplates: TaskTemplate[] = [
  {
    id: 'jee-physics-mechanics',
    name: 'JEE Physics - Mechanics',
    description: 'Complete study plan for JEE Physics Mechanics chapter',
    category: 'JEE',
    tasks: [
      {
        title: 'Read NCERT Chapter - Laws of Motion',
        description: 'Thoroughly read and understand the concepts',
        priority: 'high',
        difficultyLevel: 'medium',
        timeEstimate: 120,
        tags: ['reading', 'theory'],
      },
      {
        title: 'Solve NCERT Examples',
        description: 'Practice all examples from the chapter',
        priority: 'high',
        difficultyLevel: 'medium',
        timeEstimate: 90,
        tags: ['practice', 'examples'],
      },
      {
        title: 'Practice Previous Year Questions',
        description: 'Solve last 10 years JEE questions on mechanics',
        priority: 'high',
        difficultyLevel: 'hard',
        timeEstimate: 180,
        tags: ['pyq', 'practice'],
        examType: 'JEE_MAIN',
      },
      {
        title: 'Take Mock Test',
        description: 'Attempt a full-length mock test on mechanics',
        priority: 'medium',
        difficultyLevel: 'hard',
        timeEstimate: 60,
        tags: ['test', 'assessment'],
      },
    ],
    tags: ['physics', 'mechanics', 'jee'],
    estimatedDuration: 450,
    difficulty: 'hard',
    isPublic: true,
    createdAt: Date.now(),
    usageCount: 0,
  },
  {
    id: 'neet-biology-genetics',
    name: 'NEET Biology - Genetics',
    description: 'Comprehensive genetics study plan for NEET',
    category: 'NEET',
    tasks: [
      {
        title: 'Study Mendel\'s Laws',
        description: 'Understand the fundamental laws of inheritance',
        priority: 'high',
        difficultyLevel: 'medium',
        timeEstimate: 90,
        tags: ['theory', 'genetics'],
      },
      {
        title: 'Practice Genetic Crosses',
        description: 'Solve various types of genetic cross problems',
        priority: 'high',
        difficultyLevel: 'hard',
        timeEstimate: 120,
        tags: ['practice', 'problems'],
      },
      {
        title: 'Learn Molecular Genetics',
        description: 'DNA, RNA, and protein synthesis',
        priority: 'high',
        difficultyLevel: 'hard',
        timeEstimate: 150,
        tags: ['molecular', 'theory'],
        examType: 'NEET',
      },
      {
        title: 'Review and Test',
        description: 'Quick revision and practice test',
        priority: 'medium',
        difficultyLevel: 'medium',
        timeEstimate: 60,
        tags: ['revision', 'test'],
      },
    ],
    tags: ['biology', 'genetics', 'neet'],
    estimatedDuration: 420,
    difficulty: 'hard',
    isPublic: true,
    createdAt: Date.now(),
    usageCount: 0,
  },
];

interface TaskTemplateSystemProps {
  isOpen: boolean;
  onClose: () => void;
}

export function TaskTemplateSystem({ isOpen, onClose }: TaskTemplateSystemProps) {
  const { addTask, subjects } = useEnhancedTodoStore();
  
  const [templates, setTemplates] = useState<TaskTemplate[]>(predefinedTemplates);
  const [selectedTemplate, setSelectedTemplate] = useState<TaskTemplate | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Apply template
  const applyTemplate = useCallback(async (template: TaskTemplate, subjectId?: string) => {
    try {
      for (const taskData of template.tasks) {
        await addTask({
          ...taskData,
          subjectId,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          completionPercentage: 0,
          viewCount: 0,
        });
      }

      // Update usage count
      setTemplates(prev => prev.map(t => 
        t.id === template.id 
          ? { ...t, usageCount: t.usageCount + 1 }
          : t
      ));

      onClose();
    } catch (error) {
      console.error('Failed to apply template:', error);
    }
  }, [addTask, onClose]);

  // Get category icon
  const getCategoryIcon = useCallback((category: string) => {
    switch (category) {
      case 'JEE': return <Calculator className="h-4 w-4" />;
      case 'NEET': return <Microscope className="h-4 w-4" />;
      case 'CBSE': return <GraduationCap className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  }, []);

  // Get difficulty color
  const getDifficultyColor = useCallback((difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'hard': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Template className="h-5 w-5" />
            Task Templates
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col h-full">
          {/* Search and filters */}
          <div className="flex items-center gap-4 mb-4">
            <div className="flex-1">
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="JEE">JEE</SelectItem>
                <SelectItem value="NEET">NEET</SelectItem>
                <SelectItem value="CBSE">CBSE</SelectItem>
                <SelectItem value="Custom">Custom</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => setIsCreating(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Create Template
            </Button>
          </div>

          {/* Templates grid */}
          <div className="flex-1 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <AnimatePresence>
                {filteredTemplates.map((template, index) => (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="h-full hover:shadow-md transition-shadow cursor-pointer">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            {getCategoryIcon(template.category)}
                            <CardTitle className="text-lg">{template.name}</CardTitle>
                          </div>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => setSelectedTemplate(template)}>
                                <Copy className="h-4 w-4 mr-2" />
                                Apply Template
                              </DropdownMenuItem>
                              {!template.isPublic && (
                                <>
                                  <DropdownMenuItem>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-destructive">
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                      </CardHeader>
                      
                      <CardContent className="space-y-3">
                        {/* Template stats */}
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {Math.floor(template.estimatedDuration / 60)}h {template.estimatedDuration % 60}m
                          </div>
                          <div className="flex items-center gap-1">
                            <Target className="h-3 w-3" />
                            {template.tasks.length} tasks
                          </div>
                          {template.usageCount > 0 && (
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3" />
                              {template.usageCount} uses
                            </div>
                          )}
                        </div>

                        {/* Difficulty and category badges */}
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={cn("text-xs", getDifficultyColor(template.difficulty))}>
                            {template.difficulty}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {template.category}
                          </Badge>
                          {template.isPublic && (
                            <Badge variant="outline" className="text-xs">
                              <Sparkles className="h-3 w-3 mr-1" />
                              Official
                            </Badge>
                          )}
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-1">
                          {template.tags.slice(0, 3).map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs px-2 py-0">
                              {tag}
                            </Badge>
                          ))}
                          {template.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs px-2 py-0">
                              +{template.tags.length - 3}
                            </Badge>
                          )}
                        </div>

                        {/* Apply button */}
                        <Button
                          className="w-full"
                          onClick={() => setSelectedTemplate(template)}
                        >
                          Apply Template
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {filteredTemplates.length === 0 && (
              <div className="text-center py-12 text-muted-foreground">
                <Template className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No templates found</p>
                <p className="text-sm">Try adjusting your search or create a new template</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>

      {/* Template application dialog */}
      {selectedTemplate && (
        <Dialog open={!!selectedTemplate} onOpenChange={() => setSelectedTemplate(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Apply Template: {selectedTemplate.name}</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                This will create {selectedTemplate.tasks.length} tasks based on the template.
              </p>
              
              <div>
                <label className="text-sm font-medium">Assign to Subject (Optional)</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a subject" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(subjects).map(subject => (
                      <SelectItem key={subject.id} value={subject.id}>
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: subject.color }}
                          />
                          {subject.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedTemplate(null)}>
                Cancel
              </Button>
              <Button onClick={() => applyTemplate(selectedTemplate)}>
                Create {selectedTemplate.tasks.length} Tasks
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Dialog>
  );
}
