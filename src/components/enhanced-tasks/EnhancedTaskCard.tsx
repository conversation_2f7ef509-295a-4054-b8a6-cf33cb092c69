import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Draggable } from '@hello-pangea/dnd';
import {
  Card,
  CardContent,
  CardHeader,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Calendar,
  Clock,
  ChevronDown,
  ChevronRight,
  MoreHorizontal,
  AlertTriangle,
  CheckCircle2,
  Circle,
  BookOpen,
  Target,
  Timer,
  Eye,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedTodoItem } from '@/types/todo';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { MobileSwipeActions, useIsMobile } from './MobileSwipeActions';
import { formatDistanceToNow, format, isAfter, isBefore, addDays } from 'date-fns';

interface EnhancedTaskCardProps {
  task: EnhancedTodoItem;
  index: number;
  isBeingDragged?: boolean;
  showSubtasks?: boolean;
  depth?: number;
}

export const EnhancedTaskCard = React.memo(function EnhancedTaskCard({
  task,
  index,
  isBeingDragged = false,
  showSubtasks = true,
  depth = 0
}: EnhancedTaskCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  
  const {
    updateTask,
    deleteTask,
    selectTask,
    selectedTasks,
    getTaskHierarchy,
    calculateTaskProgress,
    subjects,
    presetExams,
  } = useEnhancedTodoStore();

  const isMobile = useIsMobile();

  const isSelected = selectedTasks.includes(task.id);
  const subtasks = showSubtasks ? getTaskHierarchy(task.id) : [];
  const hasSubtasks = subtasks.length > 0;
  const taskProgress = hasSubtasks ? calculateTaskProgress(task.id) : task.completionPercentage;

  // Get subject and exam information
  const subject = task.subjectId ? subjects[task.subjectId] : undefined;
  const exam = task.examId ? presetExams[task.examId] : undefined;

  // Calculate due date status
  const getDueDateStatus = () => {
    if (!task.dueDate) return null;
    
    const now = new Date();
    const dueDate = new Date(task.dueDate);
    const daysDiff = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff < 0) return 'overdue';
    if (daysDiff === 0) return 'today';
    if (daysDiff <= 3) return 'soon';
    return 'normal';
  };

  const dueDateStatus = getDueDateStatus();

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500/90 text-white';
      case 'medium': return 'bg-amber-500/90 text-white';
      case 'low': return 'bg-green-500/90 text-white';
      default: return 'bg-gray-500/90 text-white';
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'hard': return 'bg-purple-500/20 text-purple-700 border-purple-500/30';
      case 'medium': return 'bg-blue-500/20 text-blue-700 border-blue-500/30';
      case 'easy': return 'bg-emerald-500/20 text-emerald-700 border-emerald-500/30';
      default: return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
    }
  };

  // Get due date color
  const getDueDateColor = () => {
    switch (dueDateStatus) {
      case 'overdue': return 'text-red-600 bg-red-50 border-red-200';
      case 'today': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'soon': return 'text-amber-600 bg-amber-50 border-amber-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Handle task completion toggle
  const handleToggleComplete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    const newCompletion = task.completionPercentage === 100 ? 0 : 100;
    await updateTask(task.id, { 
      completionPercentage: newCompletion,
      updatedAt: Date.now(),
    });
  };

  // Handle task selection
  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    selectTask(task.id);
  };

  // Handle view count increment
  const handleCardClick = async () => {
    if (!isEditing) {
      await updateTask(task.id, {
        viewCount: (task.viewCount || 0) + 1,
        lastViewed: Date.now(),
      });
      setIsEditing(true);
    }
  };

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { duration: 0.3, ease: "easeOut" }
    },
    dragging: { 
      scale: 1.05, 
      rotate: 2,
      boxShadow: "0 10px 30px rgba(0,0,0,0.3)",
      transition: { duration: 0.2 }
    },
  };

  const subtaskVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: { 
      opacity: 1, 
      height: "auto",
      transition: { duration: 0.3, ease: "easeOut" }
    },
  };

  return (
    <Draggable draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <motion.div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          variants={cardVariants}
          initial="hidden"
          animate={snapshot.isDragging ? "dragging" : "visible"}
          className={`mb-3 ${depth > 0 ? `ml-${Math.min(depth * 4, 16)}` : ''}`}
        >
          <MobileSwipeActions
            onComplete={handleToggleComplete}
            onDelete={() => deleteTask(task.id)}
            onEdit={() => setIsEditing(true)}
            onTogglePriority={() => {
              const newPriority = task.priority === 'high' ? 'medium' : 'high';
              updateTask(task.id, { priority: newPriority });
            }}
            isCompleted={task.completionPercentage === 100}
          >
          <Card 
            className={`
              relative overflow-hidden transition-all duration-300 cursor-pointer
              bg-[#030303]/80 backdrop-blur-md border border-gray-800/50
              hover:border-violet-500/30 hover:bg-[#030303]/90
              ${isSelected ? 'ring-2 ring-violet-500/50 border-violet-500/50' : ''}
              ${snapshot.isDragging ? 'shadow-2xl shadow-violet-500/20' : 'shadow-lg shadow-black/20'}
              ${dueDateStatus === 'overdue' ? 'border-red-500/50 bg-red-950/20' : ''}
              ${task.completionPercentage === 100 ? 'opacity-75' : ''}
            `}
            onClick={handleCardClick}
          >
            {/* Subject color indicator */}
            {subject && (
              <div 
                className="absolute top-0 left-0 w-1 h-full"
                style={{ backgroundColor: subject.color }}
              />
            )}

            {/* Selection checkbox - Larger touch target for mobile */}
            <div className="absolute top-2 left-2 z-10">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 md:h-6 md:w-6 p-0 hover:bg-violet-500/20 touch-manipulation"
                onClick={handleSelect}
              >
                {isSelected ? (
                  <CheckCircle2 className="h-5 w-5 md:h-4 md:w-4 text-violet-400" />
                ) : (
                  <Circle className="h-5 w-5 md:h-4 md:w-4 text-gray-400" />
                )}
              </Button>
            </div>

            <CardHeader className="pb-3 pt-3 pl-12 pr-3">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  {/* Task title */}
                  <h3 className="font-onest font-semibold text-white text-sm leading-tight mb-2 truncate">
                    {task.title}
                  </h3>

                  {/* Tags and metadata */}
                  <div className="flex flex-wrap gap-1 mb-2">
                    {/* Priority badge */}
                    <Badge className={`text-xs px-2 py-0.5 ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </Badge>

                    {/* Difficulty badge */}
                    <Badge 
                      variant="outline" 
                      className={`text-xs px-2 py-0.5 border ${getDifficultyColor(task.difficultyLevel)}`}
                    >
                      {task.difficultyLevel}
                    </Badge>

                    {/* Subject badge */}
                    {subject && (
                      <Badge 
                        variant="outline"
                        className="text-xs px-2 py-0.5 border-gray-600 text-gray-300"
                        style={{ borderColor: subject.color + '50', color: subject.color }}
                      >
                        <BookOpen className="h-3 w-3 mr-1" />
                        {subject.name}
                      </Badge>
                    )}

                    {/* Exam badge */}
                    {exam && (
                      <Badge 
                        variant="outline"
                        className="text-xs px-2 py-0.5 border-rose-500/50 text-rose-400"
                      >
                        <Target className="h-3 w-3 mr-1" />
                        {exam.name}
                      </Badge>
                    )}
                  </div>

                  {/* Tags */}
                  {(task.tags.length > 0 || task.chapterTags.length > 0) && (
                    <div className="flex flex-wrap gap-1 mb-2">
                      {task.tags.map((tag, idx) => (
                        <span 
                          key={idx}
                          className="text-xs px-2 py-0.5 bg-violet-500/20 text-violet-300 rounded-full"
                        >
                          #{tag}
                        </span>
                      ))}
                      {task.chapterTags.map((tag, idx) => (
                        <span 
                          key={idx}
                          className="text-xs px-2 py-0.5 bg-emerald-500/20 text-emerald-300 rounded-full"
                        >
                          📖 {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                {/* Actions dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-violet-500/20"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent 
                    align="end" 
                    className="bg-[#030303]/95 backdrop-blur-md border-gray-800"
                  >
                    <DropdownMenuItem 
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsEditing(true);
                      }}
                      className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                    >
                      Edit Task
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={handleToggleComplete}
                      className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                    >
                      {task.completionPercentage === 100 ? 'Mark Incomplete' : 'Mark Complete'}
                    </DropdownMenuItem>
                    {hasSubtasks && (
                      <DropdownMenuItem 
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsExpanded(!isExpanded);
                        }}
                        className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                      >
                        {isExpanded ? 'Collapse' : 'Expand'} Subtasks
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator className="bg-gray-800" />
                    <DropdownMenuItem 
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteTask(task.id);
                      }}
                      className="text-red-400 hover:text-red-300 hover:bg-red-500/20"
                    >
                      Delete Task
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="pt-0 pb-4 px-4">
              {/* Description */}
              {task.description && (
                <p className="text-gray-400 text-sm mb-3 line-clamp-2">
                  {task.description}
                </p>
              )}

              {/* Progress bar */}
              <div className="mb-3">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-400">Progress</span>
                  <span className="text-xs text-gray-300">{taskProgress}%</span>
                </div>
                <Progress 
                  value={taskProgress} 
                  className="h-2 bg-gray-800"
                  style={{
                    background: 'linear-gradient(90deg, rgba(139, 92, 246, 0.3) 0%, rgba(168, 85, 247, 0.3) 100%)'
                  }}
                />
              </div>

              {/* Metadata row */}
              <div className="flex items-center justify-between text-xs text-gray-400">
                <div className="flex items-center gap-3">
                  {/* Due date */}
                  {task.dueDate && (
                    <div className={`flex items-center gap-1 px-2 py-1 rounded-md border ${getDueDateColor()}`}>
                      <Calendar className="h-3 w-3" />
                      <span>
                        {dueDateStatus === 'overdue' ? 'Overdue' : 
                         dueDateStatus === 'today' ? 'Today' :
                         format(new Date(task.dueDate), 'MMM d')}
                      </span>
                    </div>
                  )}

                  {/* Time estimate */}
                  {task.timeEstimate && (
                    <div className="flex items-center gap-1">
                      <Timer className="h-3 w-3" />
                      <span>{task.timeEstimate}m</span>
                    </div>
                  )}

                  {/* View count */}
                  {task.viewCount > 0 && (
                    <div className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      <span>{task.viewCount}</span>
                    </div>
                  )}
                </div>

                {/* Subtasks indicator */}
                {hasSubtasks && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-gray-400 hover:text-white hover:bg-violet-500/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsExpanded(!isExpanded);
                    }}
                  >
                    {isExpanded ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
                    <span className="ml-1">{subtasks.length} subtask{subtasks.length !== 1 ? 's' : ''}</span>
                  </Button>
                )}
              </div>

              {/* Completion button */}
              <div className="mt-3 flex justify-end">
                <Button
                  variant={task.completionPercentage === 100 ? "default" : "outline"}
                  size="sm"
                  className={`
                    h-9 md:h-8 px-4 md:px-3 text-xs transition-all duration-300 touch-manipulation
                    ${task.completionPercentage === 100
                      ? 'bg-emerald-500 hover:bg-emerald-600 text-white'
                      : 'border-violet-500/50 text-violet-400 hover:bg-violet-500/20 hover:text-violet-300'
                    }
                  `}
                  onClick={handleToggleComplete}
                >
                  {task.completionPercentage === 100 ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 md:h-3 md:w-3 mr-1" />
                      <span className="hidden sm:inline">Completed</span>
                      <span className="sm:hidden">Done</span>
                    </>
                  ) : (
                    <>
                      <Circle className="h-4 w-4 md:h-3 md:w-3 mr-1" />
                      <span className="hidden sm:inline">Mark Done</span>
                      <span className="sm:hidden">Done</span>
                    </>
                  )}
                </Button>
              </div>
            </CardContent>

            {/* Overdue warning */}
            {dueDateStatus === 'overdue' && (
              <div className="absolute top-2 right-2">
                <AlertTriangle className="h-4 w-4 text-red-400" />
              </div>
            )}
          </Card>

          {/* Subtasks */}
          <AnimatePresence>
            {isExpanded && hasSubtasks && (
              <motion.div
                variants={subtaskVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className="mt-2"
              >
                {subtasks.map((subtask, idx) => (
                  <EnhancedTaskCard
                    key={subtask.id}
                    task={subtask}
                    index={idx}
                    depth={depth + 1}
                    showSubtasks={false}
                  />
                ))}
              </motion.div>
            )}
          </AnimatePresence>
          </MobileSwipeActions>
        </motion.div>
      )}
    </Draggable>
  );
});
