import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useGamificationStore } from '@/stores/gamificationStore';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { EnhancedTodoItem } from '@/types/todo';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Trophy,
  Star,
  Zap,
  Target,
  Award,
  Flame,
  CheckCircle2,
  TrendingUp,
  X,
} from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  experience: number;
  unlocked: boolean;
}

interface CelebrationEvent {
  type: 'task_completed' | 'streak_milestone' | 'level_up' | 'badge_earned';
  title: string;
  description: string;
  experience: number;
  icon: React.ComponentType<any>;
  color: string;
}

export function TaskGamificationSystem() {
  const { progress, addExperience, addBadge, updateStreak } = useGamificationStore();
  const { todos } = useEnhancedTodoStore();
  const [celebrations, setCelebrations] = useState<CelebrationEvent[]>([]);
  const [showProgressBar, setShowProgressBar] = useState(false);

  // Define achievements
  const achievements: Achievement[] = [
    {
      id: 'first_task',
      title: 'Getting Started',
      description: 'Complete your first task',
      icon: CheckCircle2,
      color: 'text-emerald-500',
      experience: 10,
      unlocked: progress.badges.some(b => b.id === 'first_task'),
    },
    {
      id: 'task_streak_5',
      title: 'Consistent Performer',
      description: 'Complete tasks for 5 days in a row',
      icon: Flame,
      color: 'text-orange-500',
      experience: 50,
      unlocked: progress.badges.some(b => b.id === 'task_streak_5'),
    },
    {
      id: 'task_master_50',
      title: 'Task Master',
      description: 'Complete 50 tasks',
      icon: Trophy,
      color: 'text-yellow-500',
      experience: 100,
      unlocked: progress.badges.some(b => b.id === 'task_master_50'),
    },
    {
      id: 'productivity_champion',
      title: 'Productivity Champion',
      description: 'Maintain 90%+ completion rate',
      icon: Star,
      color: 'text-violet-500',
      experience: 75,
      unlocked: progress.badges.some(b => b.id === 'productivity_champion'),
    },
  ];

  // Calculate completion statistics
  const completedTasks = todos.filter(task => task.completionPercentage === 100).length;
  const totalTasks = todos.length;
  const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  // Handle task completion events
  const handleTaskCompletion = (task: EnhancedTodoItem) => {
    const baseExperience = 10;
    const priorityMultiplier = task.priority === 'high' ? 2 : task.priority === 'medium' ? 1.5 : 1;
    const experienceGained = Math.round(baseExperience * priorityMultiplier);

    addExperience(experienceGained);

    // Create celebration event
    const celebration: CelebrationEvent = {
      type: 'task_completed',
      title: 'Task Completed!',
      description: `+${experienceGained} XP`,
      experience: experienceGained,
      icon: CheckCircle2,
      color: 'text-emerald-500',
    };

    setCelebrations(prev => [...prev, celebration]);
    setShowProgressBar(true);

    // Check for achievements
    checkAchievements();

    // Auto-hide celebration after 3 seconds
    setTimeout(() => {
      setCelebrations(prev => prev.slice(1));
    }, 3000);
  };

  // Check and unlock achievements
  const checkAchievements = () => {
    achievements.forEach(achievement => {
      if (!achievement.unlocked) {
        let shouldUnlock = false;

        switch (achievement.id) {
          case 'first_task':
            shouldUnlock = completedTasks >= 1;
            break;
          case 'task_streak_5':
            shouldUnlock = progress.currentStreak >= 5;
            break;
          case 'task_master_50':
            shouldUnlock = completedTasks >= 50;
            break;
          case 'productivity_champion':
            shouldUnlock = completionRate >= 90 && totalTasks >= 10;
            break;
        }

        if (shouldUnlock) {
          const badge = {
            id: achievement.id,
            name: achievement.title,
            description: achievement.description,
            imageUrl: '', // Could be added later
          };

          addBadge(badge);
          addExperience(achievement.experience);

          // Create achievement celebration
          const celebration: CelebrationEvent = {
            type: 'badge_earned',
            title: 'Achievement Unlocked!',
            description: achievement.title,
            experience: achievement.experience,
            icon: achievement.icon,
            color: achievement.color,
          };

          setCelebrations(prev => [...prev, celebration]);
        }
      }
    });
  };

  // Calculate experience progress for current level
  const experienceForNextLevel = 100;
  const experienceProgress = (progress.experience / experienceForNextLevel) * 100;

  return (
    <>
      {/* Progress Bar Overlay */}
      <AnimatePresence>
        {showProgressBar && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"
          >
            <Card className="bg-gray-900/95 backdrop-blur-md border-gray-700">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-500" />
                    <span className="text-white font-medium">Level {progress.level}</span>
                  </div>
                  <div className="flex-1 min-w-[200px]">
                    <Progress value={experienceProgress} className="h-2" />
                  </div>
                  <span className="text-gray-300 text-sm">
                    {progress.experience}/{experienceForNextLevel} XP
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowProgressBar(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Celebration Notifications */}
      <div className="fixed top-20 right-4 z-50 space-y-2">
        <AnimatePresence>
          {celebrations.map((celebration, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: 300, scale: 0.8 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 300, scale: 0.8 }}
              className="bg-gray-900/95 backdrop-blur-md border border-gray-700 rounded-lg p-4 min-w-[280px]"
            >
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full bg-gray-800 ${celebration.color}`}>
                  <celebration.icon className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="text-white font-medium">{celebration.title}</h4>
                  <p className="text-gray-300 text-sm">{celebration.description}</p>
                </div>
                {celebration.experience > 0 && (
                  <div className="text-yellow-500 font-bold">
                    +{celebration.experience} XP
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Achievement Progress Panel */}
      <Card className="bg-gray-900/50 backdrop-blur-md border-gray-700">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              Achievements
            </h3>
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Award className="h-4 w-4" />
              {progress.badges.length}/{achievements.length}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {achievements.map((achievement) => (
              <div
                key={achievement.id}
                className={`p-3 rounded-lg border transition-all ${
                  achievement.unlocked
                    ? 'bg-gray-800/50 border-gray-600'
                    : 'bg-gray-900/30 border-gray-800'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`p-2 rounded-full ${
                      achievement.unlocked ? 'bg-gray-700' : 'bg-gray-800'
                    } ${achievement.unlocked ? achievement.color : 'text-gray-500'}`}
                  >
                    <achievement.icon className="h-4 w-4" />
                  </div>
                  <div className="flex-1">
                    <h4
                      className={`font-medium ${
                        achievement.unlocked ? 'text-white' : 'text-gray-500'
                      }`}
                    >
                      {achievement.title}
                    </h4>
                    <p className="text-xs text-gray-400">{achievement.description}</p>
                  </div>
                  {achievement.unlocked && (
                    <CheckCircle2 className="h-4 w-4 text-emerald-500" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );
}

// Hook to integrate gamification with task completion
export function useTaskGamification() {
  const { addExperience, updateStreak } = useGamificationStore();

  const celebrateTaskCompletion = (task: EnhancedTodoItem) => {
    const baseExperience = 10;
    const priorityMultiplier = task.priority === 'high' ? 2 : task.priority === 'medium' ? 1.5 : 1;
    const experienceGained = Math.round(baseExperience * priorityMultiplier);

    addExperience(experienceGained);
    updateStreak();

    // Trigger celebration animation
    const event = new CustomEvent('taskCompleted', {
      detail: { task, experience: experienceGained }
    });
    window.dispatchEvent(event);
  };

  return { celebrateTaskCompletion };
}
