import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { debounce } from 'lodash';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  X,
  CalendarIcon,
  BookOpen,
  Target,
  Tag,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  Save,
  Trash2,
  RotateCcw,
} from 'lucide-react';
import { format } from 'date-fns';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { FilterState } from '@/types/todo';

interface SearchAndFilterPanelProps {
  className?: string;
}

export function SearchAndFilterPanel({ className = '' }: SearchAndFilterPanelProps) {
  const {
    search,
    subjects,
    exams,
    setSearchQuery,
    setFilters,
    clearFilters,
    saveFilter,
    loadFilter,
    deleteFilter,
    getFilteredTasks,
  } = useEnhancedTodoStore();

  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [saveFilterName, setSaveFilterName] = useState('');
  const [showSaveFilter, setShowSaveFilter] = useState(false);

  // Debounced search
  const [searchInput, setSearchInput] = useState(search.query);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchQuery(searchInput);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchInput, setSearchQuery]);

  // Get filtered tasks count
  const filteredTasksCount = useMemo(() => {
    return getFilteredTasks().length;
  }, [getFilteredTasks]);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    const { filters } = search;
    return (
      filters.subjects.length > 0 ||
      filters.priorities.length > 0 ||
      filters.difficultyLevels.length > 0 ||
      filters.tags.length > 0 ||
      filters.examIds.length > 0 ||
      filters.showOverdue ||
      !filters.showCompleted ||
      filters.dateRange.start ||
      filters.dateRange.end
    );
  }, [search.filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters({ [key]: value });
  };

  // Handle array filter toggle
  const handleArrayFilterToggle = (key: keyof FilterState, value: string) => {
    const currentArray = search.filters[key] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    
    setFilters({ [key]: newArray });
  };

  // Handle save filter
  const handleSaveFilter = () => {
    if (saveFilterName.trim()) {
      saveFilter(saveFilterName.trim(), search.filters);
      setSaveFilterName('');
      setShowSaveFilter(false);
    }
  };

  // Quick filter buttons
  const quickFilters = [
    {
      label: 'Overdue',
      icon: AlertTriangle,
      color: 'text-red-400 border-red-500/50 hover:bg-red-500/20',
      active: search.filters.showOverdue,
      onClick: () => handleFilterChange('showOverdue', !search.filters.showOverdue),
    },
    {
      label: 'High Priority',
      icon: Star,
      color: 'text-amber-400 border-amber-500/50 hover:bg-amber-500/20',
      active: search.filters.priorities.includes('high'),
      onClick: () => handleArrayFilterToggle('priorities', 'high'),
    },
    {
      label: 'Completed',
      icon: CheckCircle,
      color: 'text-emerald-400 border-emerald-500/50 hover:bg-emerald-500/20',
      active: search.filters.showCompleted,
      onClick: () => handleFilterChange('showCompleted', !search.filters.showCompleted),
    },
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          placeholder="Search tasks, tags, descriptions..."
          className="pl-10 pr-4 bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-violet-500"
        />
        {searchInput && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-gray-400 hover:text-white"
            onClick={() => setSearchInput('')}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Quick Filters and Filter Button */}
      <div className="flex items-center justify-between gap-2">
        <div className="flex items-center gap-2 flex-wrap">
          {quickFilters.map((filter) => (
            <Button
              key={filter.label}
              variant="outline"
              size="sm"
              className={`
                h-8 px-3 text-xs transition-all duration-200
                ${filter.active 
                  ? `${filter.color} bg-opacity-20` 
                  : 'border-gray-600 text-gray-400 hover:text-white hover:border-gray-500'
                }
              `}
              onClick={filter.onClick}
            >
              <filter.icon className="h-3 w-3 mr-1" />
              {filter.label}
            </Button>
          ))}
        </div>

        <div className="flex items-center gap-2">
          {/* Results count */}
          <span className="text-xs text-gray-400">
            {filteredTasksCount} task{filteredTasksCount !== 1 ? 's' : ''}
          </span>

          {/* Advanced Filter Button */}
          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={`
                  h-8 px-3 text-xs transition-all duration-200
                  ${hasActiveFilters 
                    ? 'border-violet-500/50 text-violet-400 bg-violet-500/20' 
                    : 'border-gray-600 text-gray-400 hover:text-white hover:border-gray-500'
                  }
                `}
              >
                <Filter className="h-3 w-3 mr-1" />
                Filters
                {hasActiveFilters && (
                  <Badge className="ml-2 h-4 w-4 p-0 bg-violet-500 text-white text-xs flex items-center justify-center">
                    !
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent 
              className="w-80 p-4 bg-[#030303]/95 backdrop-blur-md border-gray-800 text-white"
              align="end"
            >
              <div className="space-y-4">
                {/* Filter Header */}
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-white">Advanced Filters</h3>
                  <div className="flex items-center gap-2">
                    {hasActiveFilters && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearFilters}
                        className="h-6 px-2 text-xs text-gray-400 hover:text-white"
                      >
                        <RotateCcw className="h-3 w-3 mr-1" />
                        Clear
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsFilterOpen(false)}
                      className="h-6 w-6 p-0 text-gray-400 hover:text-white"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <Separator className="bg-gray-700" />

                {/* Subject Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    Subjects
                  </label>
                  <div className="space-y-2">
                    {Object.values(subjects).map((subject) => (
                      <div key={subject.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`subject-${subject.id}`}
                          checked={search.filters.subjects.includes(subject.id)}
                          onCheckedChange={() => handleArrayFilterToggle('subjects', subject.id)}
                          className="border-gray-600 data-[state=checked]:bg-violet-500 data-[state=checked]:border-violet-500"
                        />
                        <label
                          htmlFor={`subject-${subject.id}`}
                          className="text-sm text-gray-300 flex items-center gap-2 cursor-pointer"
                        >
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: subject.color }}
                          />
                          {subject.name}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator className="bg-gray-700" />

                {/* Priority Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Priority</label>
                  <div className="flex gap-2">
                    {['low', 'medium', 'high'].map((priority) => (
                      <Button
                        key={priority}
                        variant="outline"
                        size="sm"
                        className={`
                          h-8 px-3 text-xs transition-all duration-200
                          ${search.filters.priorities.includes(priority as any)
                            ? 'border-violet-500/50 text-violet-400 bg-violet-500/20'
                            : 'border-gray-600 text-gray-400 hover:text-white hover:border-gray-500'
                          }
                        `}
                        onClick={() => handleArrayFilterToggle('priorities', priority)}
                      >
                        {priority.charAt(0).toUpperCase() + priority.slice(1)}
                      </Button>
                    ))}
                  </div>
                </div>

                <Separator className="bg-gray-700" />

                {/* Difficulty Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Difficulty</label>
                  <div className="flex gap-2">
                    {['easy', 'medium', 'hard'].map((difficulty) => (
                      <Button
                        key={difficulty}
                        variant="outline"
                        size="sm"
                        className={`
                          h-8 px-3 text-xs transition-all duration-200
                          ${search.filters.difficultyLevels.includes(difficulty as any)
                            ? 'border-violet-500/50 text-violet-400 bg-violet-500/20'
                            : 'border-gray-600 text-gray-400 hover:text-white hover:border-gray-500'
                          }
                        `}
                        onClick={() => handleArrayFilterToggle('difficultyLevels', difficulty)}
                      >
                        {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                      </Button>
                    ))}
                  </div>
                </div>

                <Separator className="bg-gray-700" />

                {/* Exam Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Exams
                  </label>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {Object.values(exams).map((exam) => (
                      <div key={exam.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`exam-${exam.id}`}
                          checked={search.filters.examIds.includes(exam.id)}
                          onCheckedChange={() => handleArrayFilterToggle('examIds', exam.id)}
                          className="border-gray-600 data-[state=checked]:bg-violet-500 data-[state=checked]:border-violet-500"
                        />
                        <label
                          htmlFor={`exam-${exam.id}`}
                          className="text-sm text-gray-300 cursor-pointer flex-1"
                        >
                          <div className="flex items-center justify-between">
                            <span>{exam.name}</span>
                            <span className="text-xs text-gray-400">
                              {format(new Date(exam.date), 'MMM d')}
                            </span>
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator className="bg-gray-700" />

                {/* Date Range Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4" />
                    Due Date Range
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs bg-gray-800/50 border-gray-700 text-white hover:bg-gray-700/50"
                        >
                          {search.filters.dateRange.start 
                            ? format(search.filters.dateRange.start, 'MMM d')
                            : 'Start date'
                          }
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 bg-gray-800 border-gray-700">
                        <Calendar
                          mode="single"
                          selected={search.filters.dateRange.start}
                          onSelect={(date) => handleFilterChange('dateRange', {
                            ...search.filters.dateRange,
                            start: date,
                          })}
                          className="text-white"
                        />
                      </PopoverContent>
                    </Popover>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs bg-gray-800/50 border-gray-700 text-white hover:bg-gray-700/50"
                        >
                          {search.filters.dateRange.end 
                            ? format(search.filters.dateRange.end, 'MMM d')
                            : 'End date'
                          }
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 bg-gray-800 border-gray-700">
                        <Calendar
                          mode="single"
                          selected={search.filters.dateRange.end}
                          onSelect={(date) => handleFilterChange('dateRange', {
                            ...search.filters.dateRange,
                            end: date,
                          })}
                          className="text-white"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <Separator className="bg-gray-700" />

                {/* Status Options */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Status Options</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="show-overdue"
                        checked={search.filters.showOverdue}
                        onCheckedChange={(checked) => handleFilterChange('showOverdue', checked)}
                        className="border-gray-600 data-[state=checked]:bg-red-500 data-[state=checked]:border-red-500"
                      />
                      <label htmlFor="show-overdue" className="text-sm text-gray-300 cursor-pointer">
                        Show only overdue tasks
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="show-completed"
                        checked={search.filters.showCompleted}
                        onCheckedChange={(checked) => handleFilterChange('showCompleted', checked)}
                        className="border-gray-600 data-[state=checked]:bg-emerald-500 data-[state=checked]:border-emerald-500"
                      />
                      <label htmlFor="show-completed" className="text-sm text-gray-300 cursor-pointer">
                        Show completed tasks
                      </label>
                    </div>
                  </div>
                </div>

                {/* Saved Filters */}
                {search.savedFilters.length > 0 && (
                  <>
                    <Separator className="bg-gray-700" />
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300">Saved Filters</label>
                      <div className="space-y-1">
                        {search.savedFilters.map((savedFilter) => (
                          <div key={savedFilter.id} className="flex items-center justify-between">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 px-2 text-xs text-gray-400 hover:text-white justify-start"
                              onClick={() => loadFilter(savedFilter.id)}
                            >
                              {savedFilter.name}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-400"
                              onClick={() => deleteFilter(savedFilter.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {/* Save Filter */}
                {hasActiveFilters && (
                  <>
                    <Separator className="bg-gray-700" />
                    <div className="space-y-2">
                      {!showSaveFilter ? (
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full h-8 text-xs border-gray-600 text-gray-400 hover:text-white hover:border-gray-500"
                          onClick={() => setShowSaveFilter(true)}
                        >
                          <Save className="h-3 w-3 mr-1" />
                          Save Current Filters
                        </Button>
                      ) : (
                        <div className="flex gap-2">
                          <Input
                            value={saveFilterName}
                            onChange={(e) => setSaveFilterName(e.target.value)}
                            placeholder="Filter name..."
                            className="h-8 text-xs bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400"
                            onKeyPress={(e) => e.key === 'Enter' && handleSaveFilter()}
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-2 text-xs border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
                            onClick={handleSaveFilter}
                          >
                            <Save className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                            onClick={() => {
                              setShowSaveFilter(false);
                              setSaveFilterName('');
                            }}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Active Filters Display */}
      <AnimatePresence>
        {hasActiveFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="flex flex-wrap gap-2"
          >
            {search.filters.subjects.map((subjectId) => {
              const subject = subjects[subjectId];
              return subject ? (
                <Badge
                  key={subjectId}
                  variant="secondary"
                  className="bg-violet-500/20 text-violet-300 hover:bg-violet-500/30"
                >
                  <BookOpen className="h-3 w-3 mr-1" />
                  {subject.name}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1 hover:bg-violet-500/40"
                    onClick={() => handleArrayFilterToggle('subjects', subjectId)}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              ) : null;
            })}

            {search.filters.priorities.map((priority) => (
              <Badge
                key={priority}
                variant="secondary"
                className="bg-amber-500/20 text-amber-300 hover:bg-amber-500/30"
              >
                {priority} priority
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-1 hover:bg-amber-500/40"
                  onClick={() => handleArrayFilterToggle('priorities', priority)}
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            ))}

            {search.filters.difficultyLevels.map((difficulty) => (
              <Badge
                key={difficulty}
                variant="secondary"
                className="bg-blue-500/20 text-blue-300 hover:bg-blue-500/30"
              >
                {difficulty} difficulty
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-1 hover:bg-blue-500/40"
                  onClick={() => handleArrayFilterToggle('difficultyLevels', difficulty)}
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            ))}

            {search.filters.showOverdue && (
              <Badge
                variant="secondary"
                className="bg-red-500/20 text-red-300 hover:bg-red-500/30"
              >
                <AlertTriangle className="h-3 w-3 mr-1" />
                Overdue only
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-1 hover:bg-red-500/40"
                  onClick={() => handleFilterChange('showOverdue', false)}
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}

            {!search.filters.showCompleted && (
              <Badge
                variant="secondary"
                className="bg-gray-500/20 text-gray-300 hover:bg-gray-500/30"
              >
                Hide completed
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-1 hover:bg-gray-500/40"
                  onClick={() => handleFilterChange('showCompleted', true)}
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
