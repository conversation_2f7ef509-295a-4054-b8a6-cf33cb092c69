import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { EnhancedTodoItem } from '@/types/todo';
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  addMonths,
  subMonths,
  isToday,
  getDay,
  startOfWeek,
  endOfWeek,
  addWeeks,
  subWeeks,
  isAfter,
  isBefore,
  addDays,
  parseISO,
} from 'date-fns';
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  Clock,
  AlertTriangle,
  CheckCircle2,
  Plus,
  Filter,
  Grid3X3,
  List,
} from 'lucide-react';
import { cn } from '@/lib/utils';

type ViewMode = 'month' | 'week';

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  tasks: EnhancedTodoItem[];
  isToday: boolean;
  isOverdue: boolean;
}

export function EnhancedCalendarView() {
  const {
    getFilteredTasks,
    updateTask,
    subjects,
    presetExams,
  } = useEnhancedTodoStore();

  const [viewMode, setViewMode] = useState<ViewMode>('month');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const tasks = getFilteredTasks();

  // Group tasks by date
  const tasksByDate = useMemo(() => {
    const grouped: Record<string, EnhancedTodoItem[]> = {};
    
    tasks.forEach(task => {
      if (task.dueDate) {
        const dateKey = format(new Date(task.dueDate), 'yyyy-MM-dd');
        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push(task);
      }
    });

    return grouped;
  }, [tasks]);

  // Generate calendar days
  const calendarDays = useMemo(() => {
    const start = viewMode === 'month' 
      ? startOfWeek(startOfMonth(currentDate))
      : startOfWeek(currentDate);
    
    const end = viewMode === 'month'
      ? endOfWeek(endOfMonth(currentDate))
      : endOfWeek(currentDate);

    const days = eachDayOfInterval({ start, end });

    return days.map(date => {
      const dateKey = format(date, 'yyyy-MM-dd');
      const dayTasks = tasksByDate[dateKey] || [];
      const isCurrentMonth = viewMode === 'week' || isSameMonth(date, currentDate);
      const today = new Date();
      const isOverdue = dayTasks.some(task => 
        task.dueDate && isBefore(new Date(task.dueDate), today) && task.completionPercentage < 100
      );

      return {
        date,
        isCurrentMonth,
        tasks: dayTasks,
        isToday: isToday(date),
        isOverdue,
      } as CalendarDay;
    });
  }, [currentDate, viewMode, tasksByDate]);

  // Navigation handlers
  const navigatePrevious = useCallback(() => {
    setCurrentDate(prev => 
      viewMode === 'month' ? subMonths(prev, 1) : subWeeks(prev, 1)
    );
  }, [viewMode]);

  const navigateNext = useCallback(() => {
    setCurrentDate(prev => 
      viewMode === 'month' ? addMonths(prev, 1) : addWeeks(prev, 1)
    );
  }, [viewMode]);

  const goToToday = useCallback(() => {
    setCurrentDate(new Date());
  }, []);

  // Drag and drop handler
  const handleDragEnd = useCallback(async (result: DropResult) => {
    if (!result.destination) return;

    const taskId = result.draggableId;
    const newDateKey = result.destination.droppableId;
    
    try {
      const newDate = parseISO(newDateKey);
      await updateTask(taskId, { dueDate: newDate.getTime() });
    } catch (error) {
      console.error('Failed to update task date:', error);
    }
  }, [updateTask]);

  // Get priority color
  const getPriorityColor = useCallback((priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  }, []);

  // Get subject color
  const getSubjectColor = useCallback((subjectId?: string) => {
    if (!subjectId || !subjects[subjectId]) return 'bg-gray-500';
    return subjects[subjectId].color;
  }, [subjects]);

  // Render task item
  const renderTaskItem = useCallback((task: EnhancedTodoItem, index: number) => (
    <Draggable key={task.id} draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <motion.div
          ref={provided.innerRef}
          {...(provided.draggableProps as any)}
          {...provided.dragHandleProps}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.05 }}
          className={cn(
            "p-2 mb-1 rounded-md text-xs cursor-pointer transition-all duration-200",
            "border border-border/50 hover:border-border",
            snapshot.isDragging && "shadow-lg rotate-2 scale-105",
            task.completionPercentage === 100 && "opacity-60 line-through"
          )}
          style={{
            backgroundColor: getSubjectColor(task.subjectId) + '20',
            borderLeftColor: getSubjectColor(task.subjectId),
            borderLeftWidth: '3px',
          }}
        >
          <div className="flex items-center justify-between">
            <span className="font-medium truncate flex-1">{task.title}</span>
            <div className="flex items-center gap-1 ml-2">
              <div 
                className={cn("w-2 h-2 rounded-full", getPriorityColor(task.priority))}
              />
              {task.completionPercentage === 100 && (
                <CheckCircle2 className="w-3 h-3 text-green-500" />
              )}
            </div>
          </div>
          
          {task.timeEstimate && (
            <div className="flex items-center gap-1 mt-1 text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>{Math.floor(task.timeEstimate / 60)}h {task.timeEstimate % 60}m</span>
            </div>
          )}
          
          {task.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-1">
              {task.tags.slice(0, 2).map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                  {tag}
                </Badge>
              ))}
              {task.tags.length > 2 && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  +{task.tags.length - 2}
                </Badge>
              )}
            </div>
          )}
        </motion.div>
      )}
    </Draggable>
  ), [getPriorityColor, getSubjectColor]);

  // Render calendar day
  const renderCalendarDay = useCallback((day: CalendarDay) => {
    const dateKey = format(day.date, 'yyyy-MM-dd');
    const dayNumber = format(day.date, 'd');
    const isSelected = selectedDate && isSameDay(day.date, selectedDate);

    return (
      <Droppable key={dateKey} droppableId={dateKey}>
        {(provided, snapshot) => (
          <motion.div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={cn(
              "min-h-[120px] p-2 border border-border/30 transition-all duration-200",
              "hover:border-border/60 hover:bg-muted/20",
              !day.isCurrentMonth && "opacity-40 bg-muted/10",
              day.isToday && "bg-primary/10 border-primary/30",
              isSelected && "bg-accent/20 border-accent",
              day.isOverdue && "bg-destructive/10 border-destructive/30",
              snapshot.isDraggingOver && "bg-accent/30 border-accent scale-[1.02]"
            )}
            onClick={() => setSelectedDate(day.date)}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
          >
            {/* Day header */}
            <div className="flex items-center justify-between mb-2">
              <span className={cn(
                "text-sm font-medium",
                day.isToday && "text-primary font-bold",
                !day.isCurrentMonth && "text-muted-foreground"
              )}>
                {dayNumber}
              </span>
              
              {day.tasks.length > 0 && (
                <div className="flex items-center gap-1">
                  {day.isOverdue && (
                    <AlertTriangle className="w-3 h-3 text-destructive" />
                  )}
                  <Badge variant="secondary" className="text-xs px-1 py-0">
                    {day.tasks.length}
                  </Badge>
                </div>
              )}
            </div>

            {/* Tasks */}
            <div className="space-y-1 max-h-[80px] overflow-y-auto">
              {day.tasks.map((task, index) => renderTaskItem(task, index))}
              {provided.placeholder}
            </div>

            {/* Add task indicator */}
            {snapshot.isDraggingOver && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-2 p-2 border-2 border-dashed border-accent rounded-md text-center text-xs text-accent"
              >
                Drop task here
              </motion.div>
            )}
          </motion.div>
        )}
      </Droppable>
    );
  }, [selectedDate, renderTaskItem]);

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="space-y-4">
        {/* Calendar Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h2 className="text-2xl font-bold">
              {format(currentDate, viewMode === 'month' ? 'MMMM yyyy' : 'MMM dd, yyyy')}
            </h2>
            
            <div className="flex items-center gap-1">
              <Button variant="outline" size="sm" onClick={navigatePrevious}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={goToToday}>
                Today
              </Button>
              <Button variant="outline" size="sm" onClick={navigateNext}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <div className="flex items-center border rounded-md">
              <Button
                variant={viewMode === 'month' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('month')}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4 mr-1" />
                Month
              </Button>
              <Button
                variant={viewMode === 'week' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('week')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4 mr-1" />
                Week
              </Button>
            </div>
          </div>
        </div>

        {/* Calendar Grid */}
        <Card>
          <CardContent className="p-0">
            {/* Day headers */}
            <div className="grid grid-cols-7 border-b">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="p-3 text-center text-sm font-medium text-muted-foreground border-r last:border-r-0">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar days */}
            <div className="grid grid-cols-7">
              {calendarDays.map(day => renderCalendarDay(day))}
            </div>
          </CardContent>
        </Card>

        {/* Legend */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Legend</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-4 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span>High Priority</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span>Medium Priority</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Low Priority</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-3 h-3 text-destructive" />
                <span>Overdue</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-3 h-3 text-green-500" />
                <span>Completed</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DragDropContext>
  );
}
