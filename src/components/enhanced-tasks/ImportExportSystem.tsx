import React, { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { EnhancedTodoItem } from '@/types/todo';
import { format } from 'date-fns';
import {
  Download,
  Upload,
  FileText,
  Database,
  CheckCircle2,
  AlertTriangle,
  X,
  FileJson,
  FileSpreadsheet,
  Loader2,
  Eye,
  Import,
  Export,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImportExportSystemProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'import' | 'export';
}

interface ImportResult {
  success: number;
  failed: number;
  errors: string[];
  duplicates: number;
}

export function ImportExportSystem({ isOpen, onClose, mode }: ImportExportSystemProps) {
  const {
    getFilteredTasks,
    addTask,
    subjects,
    bulkUpdateTasks,
  } = useEnhancedTodoStore();

  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<'csv' | 'json'>('csv');
  const [importData, setImportData] = useState('');
  const [previewData, setPreviewData] = useState<Partial<EnhancedTodoItem>[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const tasks = getFilteredTasks();

  // Export functionality
  const exportToCSV = useCallback(() => {
    const headers = [
      'Title',
      'Description',
      'Priority',
      'Due Date',
      'Subject',
      'Difficulty',
      'Time Estimate (min)',
      'Completion %',
      'Tags',
      'Chapter Tags',
      'Notes',
      'Created At',
    ];

    const csvData = [
      headers.join(','),
      ...tasks.map(task => [
        `"${task.title.replace(/"/g, '""')}"`,
        `"${(task.description || '').replace(/"/g, '""')}"`,
        task.priority,
        task.dueDate ? format(new Date(task.dueDate), 'yyyy-MM-dd') : '',
        task.subjectId ? (subjects[task.subjectId]?.name || '') : '',
        task.difficultyLevel,
        task.timeEstimate || '',
        task.completionPercentage,
        `"${task.tags.join(';')}"`,
        `"${task.chapterTags.join(';')}"`,
        `"${(task.notes || '').replace(/"/g, '""')}"`,
        format(new Date(task.createdAt), 'yyyy-MM-dd HH:mm:ss'),
      ].join(','))
    ].join('\n');

    downloadFile(csvData, `tasks-${format(new Date(), 'yyyy-MM-dd')}.csv`, 'text/csv');
  }, [tasks, subjects]);

  const exportToJSON = useCallback(() => {
    const exportData = {
      exportedAt: new Date().toISOString(),
      version: '1.0',
      tasks: tasks.map(task => ({
        ...task,
        subjectName: task.subjectId ? subjects[task.subjectId]?.name : undefined,
      })),
      subjects: Object.values(subjects),
    };

    const jsonData = JSON.stringify(exportData, null, 2);
    downloadFile(jsonData, `tasks-${format(new Date(), 'yyyy-MM-dd')}.json`, 'application/json');
  }, [tasks, subjects]);

  // Import functionality
  const parseCSV = useCallback((csvText: string): Partial<EnhancedTodoItem>[] => {
    const lines = csvText.trim().split('\n');
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data: Partial<EnhancedTodoItem>[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      const task: Partial<EnhancedTodoItem> = {
        title: values[0] || `Imported Task ${i}`,
        description: values[1] || '',
        priority: (values[2] as 'low' | 'medium' | 'high') || 'medium',
        dueDate: values[3] ? new Date(values[3]).getTime() : undefined,
        difficultyLevel: (values[5] as 'easy' | 'medium' | 'hard') || 'medium',
        timeEstimate: values[6] ? parseInt(values[6]) : undefined,
        completionPercentage: values[7] ? parseInt(values[7]) : 0,
        tags: values[8] ? values[8].split(';').filter(Boolean) : [],
        chapterTags: values[9] ? values[9].split(';').filter(Boolean) : [],
        notes: values[10] || '',
        createdAt: Date.now(),
        updatedAt: Date.now(),
        viewCount: 0,
      };

      // Find subject by name
      if (values[4]) {
        const subject = Object.values(subjects).find(s => s.name === values[4]);
        if (subject) {
          task.subjectId = subject.id;
        }
      }

      data.push(task);
    }

    return data;
  }, [subjects]);

  const parseJSON = useCallback((jsonText: string): Partial<EnhancedTodoItem>[] => {
    try {
      const parsed = JSON.parse(jsonText);
      
      if (parsed.tasks && Array.isArray(parsed.tasks)) {
        return parsed.tasks.map((task: any) => ({
          title: task.title || 'Imported Task',
          description: task.description || '',
          priority: task.priority || 'medium',
          dueDate: task.dueDate,
          difficultyLevel: task.difficultyLevel || 'medium',
          timeEstimate: task.timeEstimate,
          completionPercentage: task.completionPercentage || 0,
          tags: task.tags || [],
          chapterTags: task.chapterTags || [],
          notes: task.notes || '',
          subjectId: task.subjectId,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          viewCount: 0,
        }));
      }

      return [];
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }, []);

  const validateImportData = useCallback((data: Partial<EnhancedTodoItem>[]): string[] => {
    const errors: string[] = [];

    data.forEach((task, index) => {
      if (!task.title || task.title.trim().length === 0) {
        errors.push(`Row ${index + 1}: Title is required`);
      }
      if (task.priority && !['low', 'medium', 'high'].includes(task.priority)) {
        errors.push(`Row ${index + 1}: Invalid priority value`);
      }
      if (task.difficultyLevel && !['easy', 'medium', 'hard'].includes(task.difficultyLevel)) {
        errors.push(`Row ${index + 1}: Invalid difficulty level`);
      }
      if (task.completionPercentage && (task.completionPercentage < 0 || task.completionPercentage > 100)) {
        errors.push(`Row ${index + 1}: Completion percentage must be between 0 and 100`);
      }
    });

    return errors;
  }, []);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setImportData(content);
      
      try {
        let parsed: Partial<EnhancedTodoItem>[] = [];
        
        if (selectedFormat === 'csv') {
          parsed = parseCSV(content);
        } else {
          parsed = parseJSON(content);
        }

        const errors = validateImportData(parsed);
        if (errors.length > 0) {
          setImportResult({
            success: 0,
            failed: parsed.length,
            errors,
            duplicates: 0,
          });
        } else {
          setPreviewData(parsed);
          setShowPreview(true);
        }
      } catch (error) {
        setImportResult({
          success: 0,
          failed: 0,
          errors: [(error as Error).message],
          duplicates: 0,
        });
      }
    };

    reader.readAsText(file);
  }, [selectedFormat, parseCSV, parseJSON, validateImportData]);

  const performImport = useCallback(async () => {
    if (previewData.length === 0) return;

    setIsProcessing(true);
    setProgress(0);

    const result: ImportResult = {
      success: 0,
      failed: 0,
      errors: [],
      duplicates: 0,
    };

    try {
      for (let i = 0; i < previewData.length; i++) {
        try {
          await addTask(previewData[i]);
          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push(`Task ${i + 1}: ${(error as Error).message}`);
        }

        setProgress(((i + 1) / previewData.length) * 100);
      }

      setImportResult(result);
      setShowPreview(false);
      setPreviewData([]);
    } catch (error) {
      result.errors.push(`Import failed: ${(error as Error).message}`);
      setImportResult(result);
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  }, [previewData, addTask]);

  const downloadFile = useCallback((content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  }, []);

  const resetState = useCallback(() => {
    setImportData('');
    setPreviewData([]);
    setShowPreview(false);
    setImportResult(null);
    setProgress(0);
    setIsProcessing(false);
  }, []);

  const handleClose = useCallback(() => {
    resetState();
    onClose();
  }, [resetState, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {mode === 'import' ? (
              <>
                <Import className="h-5 w-5" />
                Import Tasks
              </>
            ) : (
              <>
                <Export className="h-5 w-5" />
                Export Tasks
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        <Tabs value={mode} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="export">Export</TabsTrigger>
            <TabsTrigger value="import">Import</TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="space-y-4">
            <div className="text-center space-y-4">
              <p className="text-muted-foreground">
                Export your {tasks.length} tasks for backup or sharing
              </p>

              <div className="grid grid-cols-2 gap-4">
                <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={exportToCSV}>
                  <CardHeader className="text-center">
                    <FileSpreadsheet className="h-12 w-12 mx-auto text-green-500" />
                    <CardTitle className="text-lg">CSV Format</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Compatible with Excel and Google Sheets
                    </p>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={exportToJSON}>
                  <CardHeader className="text-center">
                    <FileJson className="h-12 w-12 mx-auto text-blue-500" />
                    <CardTitle className="text-lg">JSON Format</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Complete data with full structure
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="import" className="space-y-4">
            {!showPreview && !importResult && (
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Select value={selectedFormat} onValueChange={(value: 'csv' | 'json') => setSelectedFormat(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    Choose File
                  </Button>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept={selectedFormat === 'csv' ? '.csv' : '.json'}
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Or paste data directly:</label>
                  <Textarea
                    value={importData}
                    onChange={(e) => setImportData(e.target.value)}
                    placeholder={`Paste your ${selectedFormat.toUpperCase()} data here...`}
                    className="min-h-[200px] font-mono text-xs"
                  />
                </div>

                {importData && (
                  <Button
                    onClick={() => {
                      try {
                        let parsed: Partial<EnhancedTodoItem>[] = [];
                        
                        if (selectedFormat === 'csv') {
                          parsed = parseCSV(importData);
                        } else {
                          parsed = parseJSON(importData);
                        }

                        const errors = validateImportData(parsed);
                        if (errors.length > 0) {
                          setImportResult({
                            success: 0,
                            failed: parsed.length,
                            errors,
                            duplicates: 0,
                          });
                        } else {
                          setPreviewData(parsed);
                          setShowPreview(true);
                        }
                      } catch (error) {
                        setImportResult({
                          success: 0,
                          failed: 0,
                          errors: [(error as Error).message],
                          duplicates: 0,
                        });
                      }
                    }}
                    className="w-full"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Import
                  </Button>
                )}
              </div>
            )}

            {showPreview && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Preview ({previewData.length} tasks)</h3>
                  <Button variant="outline" size="sm" onClick={() => setShowPreview(false)}>
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>

                <div className="max-h-[300px] overflow-y-auto border rounded-md">
                  <div className="space-y-2 p-4">
                    {previewData.slice(0, 10).map((task, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                        <div>
                          <div className="font-medium">{task.title}</div>
                          <div className="text-xs text-muted-foreground">
                            {task.priority} priority • {task.difficultyLevel} difficulty
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {task.tags?.map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                    {previewData.length > 10 && (
                      <div className="text-center text-sm text-muted-foreground">
                        ... and {previewData.length - 10} more tasks
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button onClick={performImport} disabled={isProcessing} className="flex-1">
                    {isProcessing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Import {previewData.length} Tasks
                      </>
                    )}
                  </Button>
                </div>

                {isProcessing && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Importing tasks...</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} />
                  </div>
                )}
              </div>
            )}

            {importResult && (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-4">Import Results</h3>
                  
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{importResult.success}</div>
                      <div className="text-sm text-muted-foreground">Successful</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{importResult.failed}</div>
                      <div className="text-sm text-muted-foreground">Failed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{importResult.duplicates}</div>
                      <div className="text-sm text-muted-foreground">Duplicates</div>
                    </div>
                  </div>

                  {importResult.errors.length > 0 && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="text-left">
                          <div className="font-medium mb-2">Errors encountered:</div>
                          <ul className="text-xs space-y-1">
                            {importResult.errors.slice(0, 5).map((error, index) => (
                              <li key={index}>• {error}</li>
                            ))}
                            {importResult.errors.length > 5 && (
                              <li>... and {importResult.errors.length - 5} more errors</li>
                            )}
                          </ul>
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                <Button onClick={resetState} className="w-full">
                  Import More Tasks
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
